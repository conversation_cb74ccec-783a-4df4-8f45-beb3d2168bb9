package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TaskManagementService implements TaskManagementServiceInterface
type TaskManagementService struct {
	taskRepo                 activity_cashback.ActivityTaskRepositoryInterface
	categoryRepo             activity_cashback.TaskCategoryRepositoryInterface
	progressRepo             activity_cashback.UserTaskProgressRepositoryInterface
	completionFactory        *activity_cashback.TaskCompletionRepositoryFactory
	pendingCommunityTaskRepo activity_cashback.PendingCommunityTaskRepositoryInterface
	tierService              TierManagementServiceInterface
	progressService          TaskProgressServiceInterface
}

// NewTaskManagementService creates a new TaskManagementService
func NewTaskManagementService(
	taskRepo activity_cashback.ActivityTaskRepositoryInterface,
	categoryRepo activity_cashback.TaskCategoryRepositoryInterface,
	progressRepo activity_cashback.UserTaskProgressRepositoryInterface,
	tierService TierManagementServiceInterface,
	progressService TaskProgressServiceInterface,
) TaskManagementServiceInterface {
	return &TaskManagementService{
		taskRepo:                 taskRepo,
		categoryRepo:             categoryRepo,
		progressRepo:             progressRepo,
		completionFactory:        activity_cashback.NewTaskCompletionRepositoryFactory(),
		pendingCommunityTaskRepo: activity_cashback.NewPendingCommunityTaskRepository(),
		tierService:              tierService,
		progressService:          progressService,
	}
}

// CreateTask creates a new activity task
func (s *TaskManagementService) CreateTask(ctx context.Context, task *model.ActivityTask) error {
	// Validate task data
	if err := s.validateTask(task); err != nil {
		return fmt.Errorf("task validation failed: %w", err)
	}

	// Create the task
	if err := s.taskRepo.Create(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err), zap.String("task_name", task.Name))
		return fmt.Errorf("failed to create task: %w", err)
	}

	global.GVA_LOG.Info("Task created successfully", zap.String("task_id", task.ID.String()), zap.String("task_name", task.Name))
	return nil
}

// UpdateTask updates an existing activity task
func (s *TaskManagementService) UpdateTask(ctx context.Context, task *model.ActivityTask) error {
	// Validate task data
	if err := s.validateTask(task); err != nil {
		return fmt.Errorf("task validation failed: %w", err)
	}

	// Update the task
	if err := s.taskRepo.Update(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to update task", zap.Error(err), zap.String("task_id", task.ID.String()))
		return fmt.Errorf("failed to update task: %w", err)
	}

	global.GVA_LOG.Info("Task updated successfully", zap.String("task_id", task.ID.String()), zap.String("task_name", task.Name))
	return nil
}

// DeleteTask soft deletes an activity task
func (s *TaskManagementService) DeleteTask(ctx context.Context, taskID uuid.UUID) error {
	if err := s.taskRepo.Delete(ctx, taskID); err != nil {
		global.GVA_LOG.Error("Failed to delete task", zap.Error(err), zap.String("task_id", taskID.String()))
		return fmt.Errorf("failed to delete task: %w", err)
	}

	global.GVA_LOG.Info("Task deleted successfully", zap.String("task_id", taskID.String()))
	return nil
}

// GetTaskByID retrieves a task by ID
func (s *TaskManagementService) GetTaskByID(ctx context.Context, taskID uuid.UUID) (*model.ActivityTask, error) {
	task, err := s.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("task not found: %s", taskID.String())
		}
		return nil, fmt.Errorf("failed to get task: %w", err)
	}
	return task, nil
}

// GetTasksForUser retrieves available tasks for a user
func (s *TaskManagementService) GetTasksForUser(ctx context.Context, userID uuid.UUID) ([]model.ActivityTask, error) {
	tasks, err := s.taskRepo.GetTasksForUser(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tasks for user", zap.Error(err), zap.String("user_id", userID.String()))
		return nil, fmt.Errorf("failed to get tasks for user: %w", err)
	}
	return tasks, nil
}

// GetTasksByCategory retrieves tasks by category name
func (s *TaskManagementService) GetTasksByCategory(ctx context.Context, categoryName model.TaskCategoryName) ([]model.ActivityTask, error) {
	category, err := s.categoryRepo.GetByName(ctx, categoryName)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	tasks, err := s.taskRepo.GetByCategoryID(ctx, category.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tasks by category: %w", err)
	}
	return tasks, nil
}

// CompleteTask completes a task for a user
func (s *TaskManagementService) CompleteTask(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	// Get the task
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return err
	}

	// Check if task is available
	if !task.IsAvailable() {
		return fmt.Errorf("task is not available")
	}

	// Handle consecutive checkin tasks - treat them as daily checkin + milestone check
	if task.TaskIdentifier != nil &&
		(*task.TaskIdentifier == model.TaskIDConsecutiveCheckin3 ||
			*task.TaskIdentifier == model.TaskIDConsecutiveCheckin7 ||
			*task.TaskIdentifier == model.TaskIDConsecutiveCheckin30) {

		// First, trigger daily checkin logic (update streaks for all consecutive tasks)
		if err := s.updateConsecutiveCheckinTasks(ctx, userID); err != nil {
			return fmt.Errorf("failed to update consecutive checkin tasks: %w", err)
		}

		// Get updated progress after streak update
		updatedProgress, err := s.progressService.GetTaskProgress(ctx, userID, taskID)
		if err != nil {
			return fmt.Errorf("failed to get updated task progress: %w", err)
		}

		// Check if milestone is reached
		targetDays := map[model.TaskIdentifier]int{
			model.TaskIDConsecutiveCheckin3:  3,
			model.TaskIDConsecutiveCheckin7:  7,
			model.TaskIDConsecutiveCheckin30: 30,
		}

		requiredStreak := targetDays[*task.TaskIdentifier]
		if updatedProgress.StreakCount < requiredStreak {
			// Milestone not reached yet - return success but indicate progress
			global.GVA_LOG.Info("Consecutive checkin progress updated",
				zap.String("user_id", userID.String()),
				zap.String("task_identifier", string(*task.TaskIdentifier)),
				zap.Int("current_streak", updatedProgress.StreakCount),
				zap.Int("required_streak", requiredStreak))

			// Return early - don't complete the task, just update progress
			return nil
		}

		// Milestone reached - continue with normal completion flow
		global.GVA_LOG.Info("Consecutive checkin milestone reached",
			zap.String("user_id", userID.String()),
			zap.String("task_identifier", string(*task.TaskIdentifier)),
			zap.Int("milestone", requiredStreak))
	}

	// Verify task completion
	verified, err := s.VerifyTaskCompletion(ctx, userID, taskID, verificationData)
	if err != nil {
		return fmt.Errorf("task verification failed: %w", err)
	}
	if !verified {
		return fmt.Errorf("task completion could not be verified")
	}

	// Get or create user progress
	progress, err := s.progressService.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.progressService.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return fmt.Errorf("failed to initialize task progress: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get task progress: %w", err)
		}
	}

	// Check if task can be completed again
	if !s.canCompleteTask(ctx, userID, task, progress) {
		return fmt.Errorf("task cannot be completed at this time")
	}

	// Handle consecutive checkin tasks specially
	if task.TaskIdentifier != nil &&
		(*task.TaskIdentifier == model.TaskIDConsecutiveCheckin3 ||
			*task.TaskIdentifier == model.TaskIDConsecutiveCheckin7 ||
			*task.TaskIdentifier == model.TaskIDConsecutiveCheckin30) {

		// For consecutive checkin tasks, we need to:
		// 1. Update progressValue to match current streakCount
		// 2. Complete the task
		// 3. Reset streak for next milestone

		// Get current progress to get streak count
		currentProgress, err := s.progressService.GetTaskProgress(ctx, userID, taskID)
		if err != nil {
			return fmt.Errorf("failed to get current progress for consecutive checkin: %w", err)
		}

		// Update progressValue to match streakCount before completion
		if err := s.progressService.SetProgressWithoutAutoComplete(ctx, userID, taskID, currentProgress.StreakCount); err != nil {
			return fmt.Errorf("failed to update progress value for consecutive checkin: %w", err)
		}

		// Complete the progress with points using progressive completion
		if err := s.progressService.CompleteProgressiveTaskWithPoints(ctx, userID, taskID, task.Points); err != nil {
			return fmt.Errorf("failed to complete consecutive checkin progress: %w", err)
		}

		// Reset streak to 0 so user can start working towards next milestone
		if err := s.progressService.ResetStreak(ctx, userID, taskID); err != nil {
			global.GVA_LOG.Error("Failed to reset streak after consecutive checkin completion", zap.Error(err))
			// Don't return error here as the task is already completed
		}

		global.GVA_LOG.Info("Consecutive checkin task completed with progress value updated and streak reset",
			zap.String("user_id", userID.String()),
			zap.String("task_identifier", string(*task.TaskIdentifier)),
			zap.Int("progress_value", currentProgress.StreakCount),
			zap.Int("points", task.Points))

	} else {
		// Complete the progress with points for normal tasks
		if err := s.progressService.CompleteProgressWithPoints(ctx, userID, taskID, task.Points); err != nil {
			return fmt.Errorf("failed to complete progress: %w", err)
		}

		// Special handling for DAILY_CHECKIN: update consecutive checkin tasks
		if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDDailyCheckin {
			if err := s.updateConsecutiveCheckinTasks(ctx, userID); err != nil {
				global.GVA_LOG.Error("Failed to update consecutive checkin tasks after daily checkin", zap.Error(err))
				// Don't return error here as daily checkin is already completed
			}
		}
	}

	// Add points to user
	global.GVA_LOG.Info("Attempting to add points for task completion",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("points", task.Points))

	if err := s.tierService.AddPoints(ctx, userID, task.Points, fmt.Sprintf("task_completion:%s", taskID.String())); err != nil {
		global.GVA_LOG.Error("Failed to add points for task completion", zap.Error(err), zap.String("user_id", userID.String()), zap.String("task_id", taskID.String()))
		// Don't return error here as the task is already completed
		return fmt.Errorf("failed to add points: %w", err)
	} else {
		global.GVA_LOG.Info("Successfully added points for task completion",
			zap.String("user_id", userID.String()),
			zap.String("task_id", taskID.String()),
			zap.Int("points", task.Points))
	}

	// Points earned is now handled in CompleteProgressWithPoints above

	// Record completion history using factory pattern
	if err := s.completionFactory.CreateTaskCompletion(ctx, userID, taskID, task.Points, verificationData); err != nil {
		global.GVA_LOG.Error("Failed to create completion history", zap.Error(err))
		// Don't return error here as the task is already completed
	}

	// Check for tier upgrade
	if _, err := s.tierService.CheckTierUpgrade(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to check tier upgrade", zap.Error(err), zap.String("user_id", userID.String()))
		// Don't return error here as the task is already completed
	}

	global.GVA_LOG.Info("Task completed successfully",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("points_awarded", task.Points))

	return nil
}

// CompleteTaskWithPoints completes a task and awards points automatically
// This method is designed for use by task handlers to ensure points are awarded
// when tasks are completed via automated systems (like NATS events)
func (s *TaskManagementService) CompleteTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	// Get the task
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// Check if task is available
	if !task.IsAvailable() {
		return fmt.Errorf("task is not available")
	}

	// Get or create user progress
	progress, err := s.progressService.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.progressService.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return fmt.Errorf("failed to initialize task progress: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get task progress: %w", err)
		}
	}

	// Check if task can be completed again
	if !s.canCompleteTask(ctx, userID, task, progress) {
		global.GVA_LOG.Info("Task cannot be completed at this time - already completed today",
			zap.String("user_id", userID.String()),
			zap.String("task_id", taskID.String()),
			zap.String("task_name", task.Name),
			zap.String("frequency", string(task.Frequency)),
			zap.Any("last_completed_at", progress.LastCompletedAt),
			zap.Int("completion_count", progress.CompletionCount))
		return fmt.Errorf("task already completed today")
	}

	// Handle consecutive checkin tasks - treat them as daily checkin + milestone check
	if task.TaskIdentifier != nil &&
		(*task.TaskIdentifier == model.TaskIDConsecutiveCheckin3 ||
			*task.TaskIdentifier == model.TaskIDConsecutiveCheckin7 ||
			*task.TaskIdentifier == model.TaskIDConsecutiveCheckin30) {

		// First, trigger daily checkin logic (update streaks for all consecutive tasks)
		if err := s.updateConsecutiveCheckinTasks(ctx, userID); err != nil {
			return fmt.Errorf("failed to update consecutive checkin tasks: %w", err)
		}

		// Get updated progress after streak update
		updatedProgress, err := s.progressService.GetTaskProgress(ctx, userID, taskID)
		if err != nil {
			return fmt.Errorf("failed to get updated task progress: %w", err)
		}

		// Check if milestone is reached
		targetDays := map[model.TaskIdentifier]int{
			model.TaskIDConsecutiveCheckin3:  3,
			model.TaskIDConsecutiveCheckin7:  7,
			model.TaskIDConsecutiveCheckin30: 30,
		}

		requiredStreak := targetDays[*task.TaskIdentifier]
		if updatedProgress.StreakCount < requiredStreak {
			// Milestone not reached yet - return success but indicate progress
			global.GVA_LOG.Info("Consecutive checkin progress updated in CompleteTaskWithPoints",
				zap.String("user_id", userID.String()),
				zap.String("task_identifier", string(*task.TaskIdentifier)),
				zap.Int("current_streak", updatedProgress.StreakCount),
				zap.Int("required_streak", requiredStreak))

			// Return early - don't complete the task, just update progress
			return nil
		}

		// Milestone reached - continue with normal completion flow
		global.GVA_LOG.Info("Consecutive checkin milestone reached in CompleteTaskWithPoints",
			zap.String("user_id", userID.String()),
			zap.String("task_identifier", string(*task.TaskIdentifier)),
			zap.Int("milestone", requiredStreak))
	}

	// Check if this is a progressive task (consecutive checkin tasks)
	isProgressiveTask := task.Frequency == model.FrequencyProgressive ||
		*task.TaskIdentifier == model.TaskIDConsecutiveCheckin3 ||
		*task.TaskIdentifier == model.TaskIDConsecutiveCheckin7 ||
		*task.TaskIdentifier == model.TaskIDConsecutiveCheckin30

	// Complete the progress with points
	if isProgressiveTask {
		// For progressive tasks, don't override progressValue (preserve streak-based logic)
		if err := s.progressService.CompleteProgressiveTaskWithPoints(ctx, userID, taskID, task.Points); err != nil {
			return fmt.Errorf("failed to complete progressive task progress: %w", err)
		}
	} else {
		// For normal tasks, use standard completion
		if err := s.progressService.CompleteProgressWithPoints(ctx, userID, taskID, task.Points); err != nil {
			return fmt.Errorf("failed to complete progress: %w", err)
		}
	}

	// Add points to user
	global.GVA_LOG.Info("Attempting to add points for automated task completion",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.String("task_name", task.Name),
		zap.Int("points", task.Points))

	if err := s.tierService.AddPoints(ctx, userID, task.Points, fmt.Sprintf("auto_task_completion:%s", taskID.String())); err != nil {
		global.GVA_LOG.Error("Failed to add points for automated task completion",
			zap.Error(err),
			zap.String("user_id", userID.String()),
			zap.String("task_id", taskID.String()),
			zap.String("task_name", task.Name),
			zap.Int("points", task.Points))
		// Don't return error here as the task is already completed
		return fmt.Errorf("failed to add points: %w", err)
	} else {
		global.GVA_LOG.Info("Successfully added points for automated task completion",
			zap.String("user_id", userID.String()),
			zap.String("task_id", taskID.String()),
			zap.String("task_name", task.Name),
			zap.Int("points", task.Points))
	}

	// Points earned is now handled in CompleteProgressWithPoints above

	// Record completion history using factory pattern
	if verificationData == nil {
		verificationData = make(map[string]interface{})
	}
	verificationData["completion_method"] = "automated"
	verificationData["source"] = "task_handler"

	if err := s.completionFactory.CreateTaskCompletion(ctx, userID, taskID, task.Points, verificationData); err != nil {
		global.GVA_LOG.Error("Failed to create completion history for automated task", zap.Error(err))
		// Don't return error here as the task is already completed
	}

	// Check for tier upgrade
	if _, err := s.tierService.CheckTierUpgrade(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to check tier upgrade", zap.Error(err), zap.String("user_id", userID.String()))
		// Don't return error here as the task is already completed
	}

	global.GVA_LOG.Info("Task completed automatically with points awarded",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.String("task_name", task.Name),
		zap.Int("points", task.Points))

	return nil
}

// CompleteProgressiveTaskWithPoints completes a progressive task and awards points
func (s *TaskManagementService) CompleteProgressiveTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, points int, verificationData map[string]interface{}) error {
	// Get the task
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// Complete the progressive task (preserves progressValue, uses streakCount)
	if err := s.progressService.CompleteProgressiveTaskWithPoints(ctx, userID, taskID, points); err != nil {
		return fmt.Errorf("failed to complete progressive task progress: %w", err)
	}

	// Add points to user total
	if err := s.tierService.AddPoints(ctx, userID, points, fmt.Sprintf("progressive_task_completion:%s", taskID.String())); err != nil {
		global.GVA_LOG.Error("Failed to add points for progressive task completion",
			zap.Error(err),
			zap.String("user_id", userID.String()),
			zap.String("task_id", taskID.String()),
			zap.Int("points", points))
		return fmt.Errorf("failed to add points: %w", err)
	}

	// Record completion history
	if verificationData == nil {
		verificationData = make(map[string]interface{})
	}
	verificationData["completion_method"] = "progressive"
	verificationData["source"] = "consecutive_checkin"

	if err := s.completionFactory.CreateTaskCompletion(ctx, userID, taskID, points, verificationData); err != nil {
		global.GVA_LOG.Error("Failed to create completion history for progressive task", zap.Error(err))
	}

	// Check for tier upgrade
	if _, err := s.tierService.CheckTierUpgrade(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to check tier upgrade", zap.Error(err), zap.String("user_id", userID.String()))
	}

	global.GVA_LOG.Info("Progressive task completed with points awarded",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.String("task_name", task.Name),
		zap.Int("points", points))

	return nil
}

// validateTask validates task data
func (s *TaskManagementService) validateTask(task *model.ActivityTask) error {
	if task.Name == "" {
		return fmt.Errorf("task name is required")
	}
	if task.CategoryID == 0 {
		return fmt.Errorf("category ID is required")
	}
	if task.Points < 0 {
		return fmt.Errorf("points cannot be negative")
	}
	if task.StartDate != nil && task.EndDate != nil && task.StartDate.After(*task.EndDate) {
		return fmt.Errorf("start date cannot be after end date")
	}
	return nil
}

// canCompleteTask checks if a task can be completed based on its category and user progress
func (s *TaskManagementService) canCompleteTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, progress *model.UserTaskProgress) bool {
	switch task.Category.Name {
	case model.CategoryDaily:
		// For daily tasks, check both progress and completion history
		if task.Frequency == model.FrequencyDaily {
			return !s.isDailyTaskCompletedToday(ctx, userID, task, progress)
		}
		return true
	case model.CategoryCommunity:
		// Community tasks can be completed based on their frequency
		switch task.Frequency {
		case model.FrequencyOneTime:
			// Check both progress status AND completion history for one-time tasks
			if progress.Status == model.TaskStatusCompleted || progress.Status == model.TaskStatusClaimed {
				return false
			}
			// Also check completion history table
			return !s.isOneTimeTaskCompleted(ctx, userID, task)
		case model.FrequencyDaily:
			// Same logic as daily tasks - check both progress and completion history
			return !s.isDailyTaskCompletedToday(ctx, userID, task, progress)
		case model.FrequencyUnlimited:
			return true
		default:
			return true
		}
	case model.CategoryTrading:
		// Trading tasks can be completed based on their frequency
		switch task.Frequency {
		case model.FrequencyOneTime:
			// Check both progress status AND completion history for one-time tasks
			if progress.Status == model.TaskStatusCompleted || progress.Status == model.TaskStatusClaimed {
				return false
			}
			// Also check completion history table
			return !s.isOneTimeTaskCompleted(ctx, userID, task)
		case model.FrequencyDaily:
			// Same logic as daily tasks - check both progress and completion history
			return !s.isDailyTaskCompletedToday(ctx, userID, task, progress)
		case model.FrequencyProgressive:
			return progress.Status != model.TaskStatusCompleted && progress.Status != model.TaskStatusClaimed
		case model.FrequencyUnlimited:
			return true
		default:
			return true
		}
	default:
		return false
	}
}

// isDailyTaskCompletedToday checks if a daily task has been completed today
// This function checks both progress table and completion history table for consistency
func (s *TaskManagementService) isDailyTaskCompletedToday(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, progress *model.UserTaskProgress) bool {
	if task.Frequency != model.FrequencyDaily {
		return false
	}

	// First check progress table
	if progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		if today.Equal(lastCompleted) {
			return true // Already completed today based on progress
		}
	}

	// Also check completion history table for daily tasks
	dailyRepo := s.completionFactory.GetRepositoryByFrequency(model.FrequencyDaily)
	if repo, ok := dailyRepo.(activity_cashback.DailyTaskCompletionRepositoryInterface); ok {
		completed, err := repo.HasUserCompletedTaskToday(ctx, userID, task.ID)
		if err != nil {
			global.GVA_LOG.Error("Failed to check daily completion", zap.Error(err))
			return true // Err on the side of caution - assume completed
		}
		return completed
	}

	return false
}

// isOneTimeTaskCompleted checks if a one-time task has been completed by checking completion history
func (s *TaskManagementService) isOneTimeTaskCompleted(ctx context.Context, userID uuid.UUID, task *model.ActivityTask) bool {
	if task.Frequency != model.FrequencyOneTime {
		return false
	}

	// Check completion history table for one-time tasks
	oneTimeRepo := s.completionFactory.GetRepositoryByFrequency(model.FrequencyOneTime)
	if repo, ok := oneTimeRepo.(activity_cashback.OneTimeTaskCompletionRepositoryInterface); ok {
		completion, err := repo.GetByUserAndTask(ctx, userID, task.ID)
		if err != nil {
			global.GVA_LOG.Error("Failed to check one-time completion", zap.Error(err))
			return false // Err on the side of caution - assume not completed
		}
		return completion != nil
	}

	return false
}

// UpdateTaskProgress updates task progress for a user
func (s *TaskManagementService) UpdateTaskProgress(ctx context.Context, userID, taskID uuid.UUID, progressValue int) error {
	return s.progressService.SetProgress(ctx, userID, taskID, progressValue)
}

// RefreshUserTasks refreshes task list for a user
func (s *TaskManagementService) RefreshUserTasks(ctx context.Context, userID uuid.UUID) error {
	// Get all available tasks
	tasks, err := s.GetTasksForUser(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get tasks for user: %w", err)
	}

	// Initialize progress for new tasks
	for _, task := range tasks {
		_, err := s.progressService.GetTaskProgress(ctx, userID, task.ID)
		if err == gorm.ErrRecordNotFound {
			if _, err := s.progressService.InitializeTaskProgress(ctx, userID, task.ID); err != nil {
				global.GVA_LOG.Error("Failed to initialize task progress", zap.Error(err),
					zap.String("user_id", userID.String()), zap.String("task_id", task.ID.String()))
			}
		}
	}

	// Reset tasks that need to be reset
	if err := s.resetUserTasksIfNeeded(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to reset user tasks", zap.Error(err), zap.String("user_id", userID.String()))
	}

	return nil
}

// ResetDailyTasks resets all daily tasks
func (s *TaskManagementService) ResetDailyTasks(ctx context.Context) error {
	tasks, err := s.progressRepo.GetTasksNeedingReset(ctx, model.ResetDaily)
	if err != nil {
		return fmt.Errorf("failed to get daily tasks needing reset: %w", err)
	}

	for _, progress := range tasks {
		// Special reset logic for consecutive checkin tasks
		if progress.Task.TaskIdentifier != nil &&
			(*progress.Task.TaskIdentifier == model.TaskIDConsecutiveCheckin3 ||
				*progress.Task.TaskIdentifier == model.TaskIDConsecutiveCheckin7 ||
				*progress.Task.TaskIdentifier == model.TaskIDConsecutiveCheckin30) {

			// Reset status to allow new checkin, but preserve streak and progressValue
			if progress.Status == model.TaskStatusCompleted || progress.Status == model.TaskStatusClaimed {
				now := time.Now()
				progress.Status = model.TaskStatusNotStarted
				progress.LastResetAt = &now
				progress.UpdatedAt = now
				// Keep: StreakCount, ProgressValue, CompletionCount, PointsEarned

				if err := s.progressRepo.Update(ctx, &progress); err != nil {
					global.GVA_LOG.Error("Failed to reset consecutive checkin task status", zap.Error(err),
						zap.String("user_id", progress.UserID.String()),
						zap.String("task_id", progress.TaskID.String()),
						zap.String("task_identifier", string(*progress.Task.TaskIdentifier)))
				} else {
					global.GVA_LOG.Debug("Reset consecutive checkin task status for new day",
						zap.String("task_id", progress.TaskID.String()),
						zap.String("task_identifier", string(*progress.Task.TaskIdentifier)),
						zap.Int("preserved_streak", progress.StreakCount),
						zap.Int("preserved_progress", progress.ProgressValue))
				}
			}
			continue
		}

		// Check if this is a progressive task (like other progressive tasks)
		if progress.Task.Frequency == model.FrequencyProgressive {
			// For progressive tasks, use special reset logic that preserves streak
			progress.ResetProgressive()
		} else {
			// For regular daily tasks, use normal reset
			progress.Reset()
		}

		if err := s.progressRepo.Update(ctx, &progress); err != nil {
			global.GVA_LOG.Error("Failed to reset daily task progress", zap.Error(err),
				zap.String("user_id", progress.UserID.String()),
				zap.String("task_id", progress.TaskID.String()),
				zap.String("frequency", string(progress.Task.Frequency)))
		}
	}

	global.GVA_LOG.Info("Daily tasks reset completed", zap.Int("tasks_reset", len(tasks)))
	return nil
}

// ResetWeeklyTasks resets all weekly tasks
func (s *TaskManagementService) ResetWeeklyTasks(ctx context.Context) error {
	tasks, err := s.progressRepo.GetTasksNeedingReset(ctx, model.ResetWeekly)
	if err != nil {
		return fmt.Errorf("failed to get weekly tasks needing reset: %w", err)
	}

	for _, progress := range tasks {
		progress.Reset()
		if err := s.progressRepo.Update(ctx, &progress); err != nil {
			global.GVA_LOG.Error("Failed to reset weekly task progress", zap.Error(err),
				zap.String("user_id", progress.UserID.String()), zap.String("task_id", progress.TaskID.String()))
		}
	}

	global.GVA_LOG.Info("Weekly tasks reset completed", zap.Int("tasks_reset", len(tasks)))
	return nil
}

// ResetMonthlyTasks resets all monthly tasks
func (s *TaskManagementService) ResetMonthlyTasks(ctx context.Context) error {
	tasks, err := s.progressRepo.GetTasksNeedingReset(ctx, model.ResetMonthly)
	if err != nil {
		return fmt.Errorf("failed to get monthly tasks needing reset: %w", err)
	}

	for _, progress := range tasks {
		progress.Reset()
		if err := s.progressRepo.Update(ctx, &progress); err != nil {
			global.GVA_LOG.Error("Failed to reset monthly task progress", zap.Error(err),
				zap.String("user_id", progress.UserID.String()), zap.String("task_id", progress.TaskID.String()))
		}
	}

	global.GVA_LOG.Info("Monthly tasks reset completed", zap.Int("tasks_reset", len(tasks)))
	return nil
}

// resetUserTasksIfNeeded resets user tasks that need to be reset
func (s *TaskManagementService) resetUserTasksIfNeeded(ctx context.Context, userID uuid.UUID) error {
	userProgress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user progress: %w", err)
	}

	for _, progress := range userProgress {
		if progress.Task.ResetPeriod != nil && progress.ShouldReset(*progress.Task.ResetPeriod) {
			progress.Reset()
			if err := s.progressRepo.Update(ctx, &progress); err != nil {
				global.GVA_LOG.Error("Failed to reset task progress", zap.Error(err),
					zap.String("user_id", userID.String()), zap.String("task_id", progress.TaskID.String()))
			}
		}
	}

	return nil
}

// VerifyTaskCompletion verifies task completion based on task type and conditions
func (s *TaskManagementService) VerifyTaskCompletion(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) (bool, error) {
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return false, err
	}

	// Get category to determine verification method
	category, err := s.categoryRepo.GetByID(ctx, task.CategoryID)
	if err != nil {
		return false, fmt.Errorf("failed to get task category: %w", err)
	}

	switch category.Name {
	case model.CategoryDaily:
		return s.verifyDailyTask(ctx, userID, task, verificationData)
	case model.CategoryCommunity:
		return s.VerifySocialMediaTask(ctx, userID, taskID, verificationData)
	case model.CategoryTrading:
		return s.VerifyTradingTask(ctx, userID, taskID, verificationData)
	default:
		return s.verifyGenericTask(ctx, userID, task, verificationData)
	}
}

// VerifySocialMediaTask verifies social media task completion
func (s *TaskManagementService) VerifySocialMediaTask(ctx context.Context, userID, taskID uuid.UUID, socialData map[string]interface{}) (bool, error) {
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return false, err
	}

	// For social media tasks, verification depends on the verification method
	if task.VerificationMethod == nil {
		return false, fmt.Errorf("verification method not specified for social media task")
	}

	switch *task.VerificationMethod {
	case model.VerificationClickVerify:
		// For click verify tasks, we trust the frontend verification
		return true, nil
	case model.VerificationManual:
		// For manual verification, mark as pending and return false for now
		// This would typically involve admin review
		return false, fmt.Errorf("manual verification required")
	case model.VerificationAuto:
		// For auto verification, check external APIs (Twitter, Telegram, etc.)
		return s.verifyExternalSocialMedia(ctx, userID, task, socialData)
	default:
		return false, fmt.Errorf("unsupported verification method")
	}
}

// VerifyTradingTask verifies trading task completion
func (s *TaskManagementService) VerifyTradingTask(ctx context.Context, userID, taskID uuid.UUID, tradingData map[string]interface{}) (bool, error) {
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return false, err
	}

	// Extract trading data
	volume, ok := tradingData["volume"].(float64)
	if !ok {
		return false, fmt.Errorf("trading volume not provided")
	}

	tradeCount, ok := tradingData["trade_count"].(int)
	if !ok {
		tradeCount = 1 // Default to 1 if not provided
	}

	// Check task conditions
	if task.Conditions != nil {
		if task.Conditions.MinTradingVolume != nil && volume < *task.Conditions.MinTradingVolume {
			return false, fmt.Errorf("trading volume requirement not met")
		}
		if task.Conditions.RequiredTradeCount != nil && tradeCount < *task.Conditions.RequiredTradeCount {
			return false, fmt.Errorf("trade count requirement not met")
		}
	}

	return true, nil
}

// verifyDailyTask verifies daily task completion
func (s *TaskManagementService) verifyDailyTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, verificationData map[string]interface{}) (bool, error) {
	// Use TaskIdentifier for better maintainability
	if task.TaskIdentifier == nil {
		// Fallback to generic verification for tasks without identifier
		return s.verifyGenericTask(ctx, userID, task, verificationData)
	}

	switch *task.TaskIdentifier {
	case model.TaskIDDailyCheckin:
		// Daily check-in is always valid if called
		return true, nil
	case model.TaskIDMemeTradeDaily, model.TaskIDPerpetualTradeDaily:
		// Verify trading activity
		return s.VerifyTradingTask(ctx, userID, task.ID, verificationData)
	case model.TaskIDMarketPageView:
		// Market check is always valid if called
		return true, nil
	case model.TaskIDConsecutiveCheckin3, model.TaskIDConsecutiveCheckin7, model.TaskIDConsecutiveCheckin30, model.TaskIDConsecutiveTradingDays:
		// Consecutive tasks are handled by their specific processors
		// For verification, we trust the processor logic
		return true, nil
	default:
		return s.verifyGenericTask(ctx, userID, task, verificationData)
	}
}

// verifyGenericTask verifies generic task completion
func (s *TaskManagementService) verifyGenericTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, verificationData map[string]interface{}) (bool, error) {
	// For generic tasks, check basic conditions
	if task.Conditions == nil {
		return true, nil
	}

	// Add more generic verification logic here as needed
	return true, nil
}

// verifyExternalSocialMedia verifies social media tasks using external APIs
func (s *TaskManagementService) verifyExternalSocialMedia(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, socialData map[string]interface{}) (bool, error) {
	// This would integrate with Twitter API, Telegram API, etc.
	// For now, return true as a placeholder
	global.GVA_LOG.Info("External social media verification not implemented",
		zap.String("task_name", task.Name),
		zap.String("user_id", userID.String()))
	return true, nil
}

// CreatePendingCommunityTask creates a pending community task that will be completed after 2 minutes
func (s *TaskManagementService) CreatePendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) (*model.PendingCommunityTask, error) {
	// Check if user already has a pending task for this task ID
	hasPending, err := s.pendingCommunityTaskRepo.HasPendingTask(ctx, userID, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing pending task: %w", err)
	}
	if hasPending {
		return nil, fmt.Errorf("user already has a pending task for this activity")
	}

	// Get task to validate it exists and is a community task
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	// Validate this is a community task that requires 2-minute wait
	if !s.isCommunityTaskWithWait(task) {
		return nil, fmt.Errorf("task does not require 2-minute wait")
	}

	// Create pending task
	now := time.Now()
	completionTime := now.Add(2 * time.Minute)

	pendingTask := &model.PendingCommunityTask{
		UserID:         userID,
		TaskID:         taskID,
		Status:         model.PendingCommunityTaskStatusPending,
		ClickedAt:      now,
		CompletionTime: &completionTime,
	}

	// Set verification data if provided
	if verificationData != nil {
		pendingTask.SetVerificationData("click_verify", "frontend", verificationData)
	}

	if err := s.pendingCommunityTaskRepo.Create(ctx, pendingTask); err != nil {
		return nil, fmt.Errorf("failed to create pending community task: %w", err)
	}

	global.GVA_LOG.Info("Created pending community task",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.String("task_name", task.Name),
		zap.Time("completion_time", completionTime))

	return pendingTask, nil
}

// GetPendingCommunityTask gets a pending community task for a user and task
func (s *TaskManagementService) GetPendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID) (*model.PendingCommunityTask, error) {
	return s.pendingCommunityTaskRepo.GetByUserAndTask(ctx, userID, taskID)
}

// HasPendingCommunityTask checks if a user has a pending community task
func (s *TaskManagementService) HasPendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID) (bool, error) {
	return s.pendingCommunityTaskRepo.HasPendingTask(ctx, userID, taskID)
}

// ProcessPendingCommunityTasks processes all pending community tasks that are ready for completion
func (s *TaskManagementService) ProcessPendingCommunityTasks(ctx context.Context) error {
	// Get all pending tasks ready for completion
	readyTasks, err := s.pendingCommunityTaskRepo.GetReadyForCompletion(ctx)
	if err != nil {
		return fmt.Errorf("failed to get ready pending tasks: %w", err)
	}

	if len(readyTasks) == 0 {
		return nil
	}

	global.GVA_LOG.Info("Processing pending community tasks", zap.Int("count", len(readyTasks)))

	processedCount := 0
	for _, pendingTask := range readyTasks {
		if err := s.processSinglePendingTask(ctx, pendingTask); err != nil {
			global.GVA_LOG.Error("Failed to process pending task",
				zap.Error(err),
				zap.String("pending_task_id", pendingTask.ID.String()),
				zap.String("user_id", pendingTask.UserID.String()),
				zap.String("task_id", pendingTask.TaskID.String()))
			continue
		}
		processedCount++
	}

	global.GVA_LOG.Info("Completed processing pending community tasks",
		zap.Int("processed", processedCount),
		zap.Int("total", len(readyTasks)))

	return nil
}

// ProcessSinglePendingTask processes a single pending community task (public method)
func (s *TaskManagementService) ProcessSinglePendingTask(ctx context.Context, pendingTask *model.PendingCommunityTask) error {
	return s.processSinglePendingTask(ctx, pendingTask)
}

// processSinglePendingTask processes a single pending community task
func (s *TaskManagementService) processSinglePendingTask(ctx context.Context, pendingTask *model.PendingCommunityTask) error {
	// Get the task to award points
	task, err := s.GetTaskByID(ctx, pendingTask.TaskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// Complete the task progress
	if err := s.progressService.CompleteProgress(ctx, pendingTask.UserID, pendingTask.TaskID); err != nil {
		return fmt.Errorf("failed to complete task progress: %w", err)
	}

	// Add points to user
	if err := s.tierService.AddPoints(ctx, pendingTask.UserID, task.Points, fmt.Sprintf("pending_task_completion:%s", pendingTask.TaskID.String())); err != nil {
		global.GVA_LOG.Error("Failed to add points for pending task completion", zap.Error(err))
		// Don't return error here as the task is already completed
	}

	// Update points earned in progress
	progress, err := s.progressService.GetTaskProgress(ctx, pendingTask.UserID, pendingTask.TaskID)
	if err == nil {
		progress.PointsEarned = task.Points
		if err := s.progressRepo.Update(ctx, progress); err != nil {
			global.GVA_LOG.Error("Failed to update points earned", zap.Error(err))
		}
	}

	// Record completion history using factory pattern
	verificationData := map[string]interface{}{
		"method": "pending_completion",
		"source": "background_job",
	}
	if pendingTask.VerificationData != nil {
		verificationData["original_data"] = pendingTask.VerificationData
	}

	if err := s.completionFactory.CreateTaskCompletion(ctx, pendingTask.UserID, pendingTask.TaskID, task.Points, verificationData); err != nil {
		global.GVA_LOG.Error("Failed to create completion history for pending task", zap.Error(err))
		// Don't return error here as the task is already completed
	}

	// Mark pending task as completed
	pendingTask.MarkAsCompleted()
	if err := s.pendingCommunityTaskRepo.Update(ctx, pendingTask); err != nil {
		global.GVA_LOG.Error("Failed to update pending task status", zap.Error(err))
		// Don't return error here as the main task is already completed
	}

	global.GVA_LOG.Info("Successfully processed pending community task",
		zap.String("user_id", pendingTask.UserID.String()),
		zap.String("task_id", pendingTask.TaskID.String()),
		zap.String("task_name", task.Name),
		zap.Int("points_awarded", task.Points))

	return nil
}

// isCommunityTaskWithWait checks if a task is a community task that requires 2-minute wait
func (s *TaskManagementService) isCommunityTaskWithWait(task *model.ActivityTask) bool {
	// Use category-based detection for better maintainability
	result := task.Category.Name == model.CategoryCommunity &&
		task.TaskIdentifier != nil &&
		model.RequiresTwoMinuteWait(*task.TaskIdentifier)

	// Debug logging
	global.GVA_LOG.Debug("Checking if task requires 2-minute wait",
		zap.String("task_id", task.ID.String()),
		zap.String("task_name", task.Name),
		zap.String("category_name", string(task.Category.Name)),
		zap.Any("task_identifier", task.TaskIdentifier),
		zap.Bool("requires_wait", result))

	return result
}

// GetVisibleConsecutiveCheckinTask returns the appropriate consecutive check-in task for a user
func (s *TaskManagementService) GetVisibleConsecutiveCheckinTask(ctx context.Context, userID uuid.UUID) (*model.ActivityTask, error) {
	// Get all consecutive check-in tasks
	allTasks, err := s.GetTasksByCategory(ctx, model.CategoryDaily)
	if err != nil {
		return nil, fmt.Errorf("failed to get daily tasks: %w", err)
	}

	var consecutiveTasks []*model.ActivityTask
	for _, task := range allTasks {
		if task.TaskIdentifier != nil {
			switch *task.TaskIdentifier {
			case model.TaskIDConsecutiveCheckin3, model.TaskIDConsecutiveCheckin7, model.TaskIDConsecutiveCheckin30:
				consecutiveTasks = append(consecutiveTasks, &task)
			}
		}
	}

	if len(consecutiveTasks) == 0 {
		return nil, nil
	}

	// Check user's current streak across all consecutive check-in tasks
	maxStreak := 0
	for _, task := range consecutiveTasks {
		progress, err := s.progressService.GetTaskProgress(ctx, userID, task.ID)
		if err != nil && err != gorm.ErrRecordNotFound {
			continue
		}
		if progress != nil && progress.StreakCount > maxStreak {
			maxStreak = progress.StreakCount
		}
	}

	// Determine which task should be visible based on current streak
	var targetTaskIdentifier model.TaskIdentifier
	if maxStreak < 3 {
		targetTaskIdentifier = model.TaskIDConsecutiveCheckin3
	} else if maxStreak >= 3 && maxStreak < 7 {
		targetTaskIdentifier = model.TaskIDConsecutiveCheckin7
	} else if maxStreak >= 7 && maxStreak < 30 {
		targetTaskIdentifier = model.TaskIDConsecutiveCheckin30
	} else if maxStreak >= 30 {
		// After completing 30 days, reset to 3-day task to start the cycle again
		targetTaskIdentifier = model.TaskIDConsecutiveCheckin3
	}

	// Find and return the appropriate task
	for _, task := range consecutiveTasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == targetTaskIdentifier {
			return task, nil
		}
	}

	return nil, nil
}

// updateConsecutiveCheckinTasks updates streak for all consecutive checkin tasks when user completes daily checkin
func (s *TaskManagementService) updateConsecutiveCheckinTasks(ctx context.Context, userID uuid.UUID) error {
	// Get all consecutive checkin tasks
	consecutiveTaskIdentifiers := []model.TaskIdentifier{
		model.TaskIDConsecutiveCheckin3,
		model.TaskIDConsecutiveCheckin7,
		model.TaskIDConsecutiveCheckin30,
	}

	// Get daily tasks to find consecutive checkin tasks
	tasks, err := s.GetTasksByCategory(ctx, model.CategoryDaily)
	if err != nil {
		return fmt.Errorf("failed to get daily tasks: %w", err)
	}

	today := time.Now().Truncate(24 * time.Hour)
	yesterday := today.Add(-24 * time.Hour)

	for _, task := range tasks {
		if task.TaskIdentifier == nil {
			continue
		}

		// Check if this is a consecutive checkin task
		isConsecutiveTask := false
		for _, identifier := range consecutiveTaskIdentifiers {
			if *task.TaskIdentifier == identifier {
				isConsecutiveTask = true
				break
			}
		}

		if !isConsecutiveTask {
			continue
		}

		// Get or create progress for this consecutive checkin task
		progress, err := s.progressService.GetTaskProgress(ctx, userID, task.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				progress, err = s.progressService.InitializeTaskProgress(ctx, userID, task.ID)
				if err != nil {
					global.GVA_LOG.Error("Failed to initialize consecutive checkin task progress", zap.Error(err))
					continue
				}
			} else {
				global.GVA_LOG.Error("Failed to get consecutive checkin task progress", zap.Error(err))
				continue
			}
		}

		// Check last checkin date
		lastCheckIn := time.Time{}
		if progress.LastCompletedAt != nil {
			lastCheckIn = progress.LastCompletedAt.Truncate(24 * time.Hour)
		}

		// Update streak based on checkin pattern
		if lastCheckIn.Equal(yesterday) {
			// Consecutive day - increment streak
			if err := s.progressService.UpdateStreak(ctx, userID, task.ID, true); err != nil {
				global.GVA_LOG.Error("Failed to increment streak for consecutive checkin task", zap.Error(err))
				continue
			}
		} else if !lastCheckIn.IsZero() && !lastCheckIn.Equal(today) {
			// Gap in checkin - reset streak and start new one
			if err := s.progressService.ResetStreak(ctx, userID, task.ID); err != nil {
				global.GVA_LOG.Error("Failed to reset streak for consecutive checkin task", zap.Error(err))
				continue
			}
			// Start new streak
			if err := s.progressService.UpdateStreak(ctx, userID, task.ID, true); err != nil {
				global.GVA_LOG.Error("Failed to start new streak for consecutive checkin task", zap.Error(err))
				continue
			}
		} else if lastCheckIn.IsZero() {
			// First time checkin - start streak
			if err := s.progressService.UpdateStreak(ctx, userID, task.ID, true); err != nil {
				global.GVA_LOG.Error("Failed to start first streak for consecutive checkin task", zap.Error(err))
				continue
			}
		}

		// Update LastCompletedAt to today for tracking consecutive days
		// We need to update the progress record to mark today as completed
		updatedProgress, err := s.progressService.GetTaskProgress(ctx, userID, task.ID)
		if err != nil {
			global.GVA_LOG.Error("Failed to get progress for updating LastCompletedAt", zap.Error(err))
			continue
		}

		// Set LastCompletedAt to today
		now := time.Now()
		updatedProgress.LastCompletedAt = &now
		updatedProgress.UpdatedAt = now

		if err := s.progressRepo.Update(ctx, updatedProgress); err != nil {
			global.GVA_LOG.Error("Failed to update LastCompletedAt for consecutive checkin task", zap.Error(err))
			continue
		}

		// Check if milestone reached and auto-complete
		targetDays := map[model.TaskIdentifier]int{
			model.TaskIDConsecutiveCheckin3:  3,
			model.TaskIDConsecutiveCheckin7:  7,
			model.TaskIDConsecutiveCheckin30: 30,
		}

		requiredStreak := targetDays[*task.TaskIdentifier]
		if updatedProgress.StreakCount == requiredStreak {
			// Milestone reached - auto complete the task
			verificationData := map[string]interface{}{
				"milestone":    requiredStreak,
				"streak_count": updatedProgress.StreakCount,
				"method":       "auto_consecutive_checkin",
				"trigger":      "daily_checkin_completion",
			}

			if err := s.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
				global.GVA_LOG.Error("Failed to auto-complete consecutive checkin milestone",
					zap.Error(err),
					zap.String("task_identifier", string(*task.TaskIdentifier)),
					zap.Int("milestone", requiredStreak))
			} else {
				global.GVA_LOG.Info("Auto-completed consecutive checkin milestone",
					zap.String("user_id", userID.String()),
					zap.String("task_identifier", string(*task.TaskIdentifier)),
					zap.Int("milestone", requiredStreak),
					zap.Int("points", task.Points))
			}
		}
	}

	return nil
}
